<script setup>
import { nextTick, onBeforeUnmount, onMounted, ref } from "vue";
import { chatMessage } from "@/http/voiceApi.js";
import { audioSpeechStar, audioSpeechStop } from "@/utils/audioUtils.js";
import { getSystemSettings } from "@/http/settingApi"; // 获取头像标题，背景
import { inject } from "vue";
// 引入聊天记录工具函数
import { saveChatHistory, saveConversationId } from "@/utils/chatHistoryUtils.js";
// window.handleVoice = handleVoice;//测试ai历史记录功能，开启预输入

// 状态
const stateBtn = ref("start");
const state = ref("");
let recognition = "";
let playTextId = 0;
let flowTextId = 0; // 定义句子流式id防止串话
const transcript = ref("");
const responseContent = ref("");
let conversation_id = ""; // 对话唯一标识
let user = "";
const btnText = ref("按下说话");
let flag = false;
let isResponse = false;
// 聊天消息数组（存储用户和AI的对话）
const messageArray = ref([]);

// 初始化语音识别
const init = async () => {
  let SpeechRecognition = "";
  if ("webkitSpeechRecognition" in window) {
    SpeechRecognition = webkitSpeechRecognition;
  } else {
    SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition || null;
  }
  recognition = new SpeechRecognition();
  recognition.lang = "zh-CN,en";
  recognition.continuous = true;
  recognition.interimResults = true;
  recognition.onresult = function (event) {
    for (let i = event.resultIndex; i < event.results.length; ++i) {
      if (event.results[i].isFinal) {
        (async () => {
          let text = event.results[i][0].transcript;
          await handleVoice(text);
        })();
      } else {
        transcript.value = event.results[i][0].transcript;
      }
    }
  };
};

// 状态文本配置（包含AI开场白）
const stateText = {
  start: {
    text: "哈喽~今天还好吗?要不要聊一下?", // AI开场白
    state: "start",
  },
  send: {
    text: "聆听中",
    state: "send",
  },
  interrupt: {
    text: "思考中... \n (请等待大约3至4秒时间)",
    state: "interrupt",
  },
};

// 状态切换处理
const stateHandler = (state1) => {
  let start = stateText[state1];
  stateBtn.value = start.state;
  flowText(start.text);
};

// 处理语音消息（核心逻辑）
async function handleVoice(text) {
  if (stateBtn.value!== "interrupt") return;
  transcript.value = "";

  // 添加用户消息到历史记录
  const userMessage = {
    message: text,
    type: "user",
    date: new Date().toLocaleTimeString(), // 时间格式化
    user: user,
  };
  messageArray.value.push(userMessage);

  // 调用后端接口发送消息
  let res = await chatMessage({
    text,
    conversation_id, // 携带对话ID
    isAudio: true,
    user
  });
  console.log("后端响应:", res);

  if (stateBtn.value!== "interrupt") return;
  state.value = "";

  // 更新对话ID并保存到本地
  conversation_id = res.data.conversation_id;
  user = res.data.user;
  

  // 添加AI回复到历史记录
  const aiMessage = {
    message: res.data.answer,
    type: "system",
    date: new Date().toLocaleTimeString(),
    user: user,
  };
  messageArray.value.push(aiMessage);

  // 保存过滤后的历史记录（自动排除开场白）
  saveChatHistory(messageArray.value);

  // 播放语音和文本流式输出
  playVoice(res.data.url);
  playText(res.data.answer);
}

// 工具函数：延迟
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// 文字流式输出（带ID防串话）
async function playText(answer) {
  playTextId++;
  const currentId = playTextId;
  transcript.value = "";
  for (let i = 0; i < answer.length; i++) {
    if (currentId!== playTextId) return; // 新消息打断旧消息
    transcript.value += answer[i];
    await sleep(80);
  }
}

// 初始文字流式输出
async function flowText(str) {
  flowTextId++;
  const currentId = flowTextId;
  state.value = "";
  for (let i = 0; i < str.length; i++) {
    if (currentId!== flowTextId) return;
    state.value += str[i];
    await sleep(80);
  }
}

// 分割文字（备用）
function splitIntoGroups(str, groupSize) {
  let result = [];
  for (let i = 0; i < str.length; i += groupSize) {
    result.push(str.slice(i, i + groupSize));
  }
  return result;
}

// 开始语音识别
function speechStart() {
  audioSpeechStar(transcript, async () => {
    console.log("语音识别结束，发送请求");
    isResponse = true;
    try {
      await handleVoice(transcript.value);
    } catch (e) {
      console.error("请求失败:", e);
    }
    isResponse = false;
  });
}

// 开始监听语音
function startListening() {
  stateHandler("send");
  try {
    let audio = audioElement.value;
    audio.muted = false;
    voicePath.value = "";
    audio.volume = 1;
    audio.load();
    audio.play();
    nextTick(() => {
      audio.pause();
      speechStart();
    });
  } catch (e) {
    console.error("音频初始化失败:", e);
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 阻止双击事件默认行为
  document.addEventListener(
    "dblclick",
    (e) => e.preventDefault(),
    { passive: false }
  );
  fetchSystemSettings();
  stateHandler("start"); // 初始显示开场白（但会被工具函数过滤不保存）
  init(); // 初始化语音识别
  user = sessionStorage.getItem('user') || '';
  conversation_id = sessionStorage.getItem('conversation_id') || '';
});
onBeforeUnmount(() => {
  saveConversationId(conversation_id); // 用工具函数保存
  sessionStorage.setItem('user', user);
})
// 停止监听
function stopListening() {
  stateHandler("interrupt");
  audioSpeechStop();
}

// 停止播放语音
function playStop() {
  const audio = audioElement.value;
  audio.muted = true;
  audio.pause();
}

// 取消当前状态
function cancelState() {
  playStop();
  audioSpeechStop();
  transcript.value = "";
  stateHandler("start");
}

// 音频相关
const audioElement = ref(null);
const voicePath = ref("");
const playVoice = (url) => {
  try {
    voicePath.value = url;
    const audio = audioElement.value;
    audio.currentTime = 0;
    audio.muted = false;
    audio.load();
    nextTick(() => audio?.play());
  } catch (e) {
    console.error("播放语音失败:", e);
  }
};

// 音频播放结束处理
const audioEndHandle = () => {
  if (isResponse) {
    transcript.value = "思考中...";
    return;
  }
  flag = false;
  const audio = audioElement.value;
  audio.muted = true;
  audio.pause();
  transcript.value = "请说话..";
  speechStart();
  stateHandler("send");
};

// 打断当前操作
const interruptClick = () => {
  playStop();
  speechStart();
  stateHandler("send");
};

// 背景图设置
const backgroundImage = ref("/images/home/<USER>");
const fetchSystemSettings = async () => {
  const settings = await getSystemSettings();
  if (settings) {
    backgroundImage.value = settings.background;
  }
};

// 适配特殊手机
const cartoonTop = inject("cartoonTop", ref("-650px"));
</script>

<template>
  <audio
    :src="voicePath"
    @ended="audioEndHandle()"
    ref="audioElement"
    controls
    style="display: none"
  ></audio>

  <div class="app">
    <div
      class="center-gif"
      :style="{ backgroundImage: `url(${backgroundImage})` }"
    ></div>
    <div class="content">
      <div class="icon">
        <router-link to="/chat">
          <img class="img" src="@/assets/text.png" alt="" />
        </router-link>
      </div>
      <div class="text-tip">文字互动</div>

      <div class="text-out-box">
        <div class="state-box" v-show="transcript === ''">
          <div>{{ state }}</div>
        </div>
        <div class="cartoon" v-show="stateBtn === 'send'">
          <img
            src="/images/home/<USER>"
            alt=""
            :style="{ top: cartoonTop }"
          />
        </div>
        <div class="text-box">{{ transcript }}</div>
      </div>
      <!--      <button class="button" @click="clickListening()">-->
      <!--        {{btnText}}-->
      <!--      </button>-->
      <div class="submit">
        <div v-show="stateBtn === 'start'" class="action-group">
          <img
            class="start"
            @click="startListening()"
            :src="`/images/home/<USER>"
            alt=""
          />
          <div class="sendtext">对话</div>
        </div>
        <div v-show="stateBtn === 'send'" class="action-group">
          <img
            class="send"
            @click="stopListening()"
            :src="`/images/home/<USER>"
            alt=""
            style="border-radius: 50%"
          />
          <div class="sendtext">发送</div>
        </div>
        <div v-show="stateBtn === 'interrupt'" class="action-group">
          <img
            class="interrupt"
            @click="interruptClick()"
            src="/images/home/<USER>"
            alt=""
          />
          <div class="sendtext">打断</div>
        </div>
      </div>
      <div class="cancel">
        <img
          @click="cancelState()"
          src="/images/home/<USER>"
          alt="终止沟通"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
body,
html,
.app {
  background-color: rgb(247, 247, 245);
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

/* 添加文本容器样式 */
.text-container {
  background-color: rgba(255, 255, 255, 0.7); /* 半透明白色背景 */
  backdrop-filter: blur(8px); /* 模糊效果 */
  border-radius: 16px; /* 圆角 */
  padding: 20px; /* 内边距 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 阴影增加层次感 */
  max-width: 80%; /* 最大宽度 */
}

.content {
  text-align: center;
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
  color: #000;
  font-weight: bold;
  .text-out-box {
    position: absolute;
    left: 50%;
    width: 80%;
    transform: translateX(-50%);
    bottom: 13rem;
    font-size: 16px;
    backdrop-filter: blur(1px);
    .text-box {
      background-color: #e0e0e0;
      color: #000;
      padding: 10px 16px;
      border-radius: 12px;
      font-size: 24px;
      line-height: 1.5;
      //当文字框内无信息时为透明
      &:empty {
        background-color: transparent;
        padding: 0;
      }
    }
  }
  .cartoon {
    position: absolute;
    top: -610px; // 距离顶部的距离
    left: 50%;
    transform: translateX(-50%);
    z-index: 10; // 确保浮在中间内容上方
    img {
      width: 50px;
      height: 70px;
    }
  }

  .state-box {
    color: #000; /* 字体颜色改为黑色 */
    background-color: rgba(220, 220, 220, 0.7); // 更明显的灰色
    backdrop-filter: blur(8px); /* 模糊效果 */
    border-radius: 12px; /* 圆角 */
    padding: 6px; /* 内边距 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 阴影增加层次感 */
    white-space: pre-wrap;
    font-size: 22px;
    width: fit-content; //根据内容自适应宽度
    max-width: 90%; //限制最长宽度，防止撑满屏
    margin: 0 auto;
  }
  .submit {
    position: absolute;
    width: 80%;
    left: 50%;
    transform: translateX(-50%);
    bottom: 5rem;
    display: flex; //  横向排列
    justify-content: center;
    gap: 20px;
    img {
      width: 80px;
      height: 80px;
    }
  }
  .action-group {
    display: flex;
    flex-direction: column; //  竖直排列图标和文字
    align-items: center;
  }

  .cancel {
    position: absolute;
    width: 80%;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0.5rem;
    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 1px solid #fff;
    }
  }
}

.icon {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.icon .img {
  width: 60px;
  height: 60px;
  margin-top: -0.5px;
}
.text-tip {
  position: absolute;
  top: 75px;
  right: 5px;
  font-size: 12px;
  color: #000; /* 字体颜色改为黑色 */
  font-weight: bold; //字体加粗
  transform: translateX(-16px); //  左移 px 对齐图标
  margin-top: 6.5px;
}
.icon-dots {
  width: 30px;
  height: 30px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ccircle cx='20' cy='20' r='5' fill='%2300008B'/%3E%3Ccircle cx='50' cy='20' r='5' fill='%2300008B'/%3E%3Ccircle cx='80' cy='20' r='5' fill='%2300008B'/%3E%3Ccircle cx='35' cy='50' r='5' fill='%2300008B'/%3E%3Ccircle cx='65' cy='50' r='5' fill='%2300008B'/%3E%3Ccircle cx='50' cy='80' r='5' fill='%2300008B'/%3E%3C/svg%3E");
  background-size: cover;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
}

.light-spot {
  position: absolute;
  width: 150px;
  height: 150px;
  background: radial-gradient(
    circle,
    rgba(63, 255, 255, 0.9) 0%,
    rgba(63, 255, 255, 0) 70%
  );
  border-radius: 50%;
  top: 50%;
  left: 50%;
  animation: pulse 2s infinite;
  z-index: 0;
}
.button {
  background: linear-gradient(
    to right,
    rgba(135, 206, 250, 0.8),
    rgba(30, 144, 255, 0.8)
  );
  color: white;
  border: none;
  padding: 12px 40px;
  border-radius: 30px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
  position: absolute;
  width: 80%;
  left: 50%;
  transform: translateX(-50%);
  bottom: 2rem;
}

.button:hover {
  background: linear-gradient(
    to right,
    rgba(30, 144, 255, 0.8),
    rgba(135, 206, 250, 0.8)
  );
}

.center-gif {
  position: absolute;
  top: 50%;
  left: 50%;
  width: calc(100% - 80px); // 两侧保留 40px 间距
  max-width: 580px;
  height: 600px;
  transform: translate(-50%, -50%);
  background-image: url("/images/home/<USER>"); // 默认图（可被动态替换）
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 0;
  opacity: 0.7;
  pointer-events: none;
}
</style>
<style lang="scss">
.bg-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background-image: url("/images/home/<USER>");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.sendtext {
  margin-top: 2px; /* 把按钮向上移动10px */
  color: #000; /* 黑色字体 */
  font-weight: bold;
  background-color: rgba(220, 220, 220, 0.7); /* 半透明白色背景 */
  backdrop-filter: blur(2px); /* 模糊效果 */
  border-radius: 12px; /* 圆角 */
  padding: 3px 10px; /* 内边距 */
  display: inline-block; /* 使背景仅包裹文字 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 轻微阴影 */
}
</style>
