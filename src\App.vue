<script setup>
import { onMounted, ref, provide } from 'vue'
import { detectDevice, isWeChatBrowser } from '@/utils/commonUtils.js'

const device = ref(detectDevice())
const wexin = ref(isWeChatBrowser())
const mainElement = ref(false)

// 新增 cartoonTop
const cartoonTop = ref('-610px')

// 计算 cartoonTop
const setCartoonTop = () => {
  const screenWidth = window.screen.width
  const screenHeight = window.screen.height

  if (screenWidth === 390 && screenHeight === 844) {
    // iPhone 12 / 12 Pro
    cartoonTop.value = '-550px'
  } else if (screenWidth === 430 && screenHeight === 926) {
    // iPhone 14 Pro Max
    cartoonTop.value = '-650px'
  } else if (screenWidth === 428 && screenHeight === 926) {
    // iPhone 12 Pro Max（分辨率是428 x 926）
    cartoonTop.value = '-550px'
  } else {
    cartoonTop.value = '-550px'
  }
}

// 提供 cartoonTop 给子组件使用
provide('cartoonTop', cartoonTop)

// ==========================
// 你的屏幕适配逻辑（保持不变）
const setScreenAdaptation = () => {
  const screenWidth = window.screen.width;
  const screenHeight = window.screen.height;
  const viewportHeight = window.innerHeight;

  if (screenHeight >= 2400 || (screenHeight / screenWidth) >= 2.1) {
    mainElement.value.style.height = `${viewportHeight}px`;
  } else if (wexin.value) {
    mainElement.value.style.height = '100vh';
  } else if (device.value === 'iOS') {
    mainElement.value.style.height = 'calc(100vh - 74px)';
  } else {
    mainElement.value.style.height = '100vh';
  }
}

onMounted(() => {
  setScreenAdaptation()
  setCartoonTop()

  window.addEventListener('orientationchange', () => {
    setTimeout(() => {
      setScreenAdaptation()
      setCartoonTop()
    }, 100)
  })
  window.addEventListener('resize', () => {
    setScreenAdaptation()
    setCartoonTop()
  })
})
</script>

<template>
  <div class="main" ref="mainElement">
    <router-view /> <!-- 路由出口 -->
  </div>
</template>

<style scoped>
.main {
  height: 100vh;
}
</style>
