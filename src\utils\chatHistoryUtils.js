// src/utils/chatHistoryUtils.js
import { getStartVoice } from '@/utils/commonUtils.js';

// 获取AI开场白文本（从公共工具函数获取，确保与页面一致）
const AI_OPENING_TEXT = getStartVoice().text;

/**
 * 保存聊天历史记录（自动过滤空消息和AI开场白）
 * @param {Array} historyMessages - 原始聊天记录数组
 */
export const saveChatHistory = (historyMessages) => {
  // 过滤逻辑：
  // 1. 排除空消息（trim() 处理纯空格） 
  // 2. 排除AI发送的开场白消息
  const filtered = historyMessages.filter(msg => {
    const isEmpty = msg.message.trim() === '';
    const isOpening = msg.type === 'system' && msg.message === AI_OPENING_TEXT;
    return!isEmpty &&!isOpening;
  });
  // 保存到sessionStorage
  sessionStorage.setItem('chatHistory', JSON.stringify(filtered));
};

/**
 * 获取过滤后的聊天历史记录
 * @returns {Array} 处理后的历史记录（无空消息和开场白）
 */
export const getChatHistory = () => {
  const raw = sessionStorage.getItem('chatHistory');
  return raw? JSON.parse(raw) : [];
};

/**
 * 保存对话ID（用于跨页面继续对话）
 * @param {string} id - 后端返回的conversation_id
 */
export const saveConversationId = (id) => {
  sessionStorage.setItem('conversationId', id);
};

/**
 * 获取对话ID（跨页面共享）
 * @returns {string} 对话ID（空字符串表示新对话）
 */
export const getConversationId = () => {
  return sessionStorage.getItem('conversationId') || '';
};
// 区分用户
export const getChatHistoryByUser = (targetUser) => {
    const historyMessages = getChatHistory();
    return historyMessages.filter(item => item.user === targetUser);
  };
  