const params = {
    // 用户参数
    secretid:  config.secretId,
    secretkey: config.secretKey,
    appid: config.appId,
    // 临时密钥参数，非必填
    // token: config.token,
    // 实时识别接口参数
    // 16k_zh（通用场景）
    // 16k_zh-PY（普粤英混合引擎，支持普通话、粤语、英语混合识别）
    engine_model_type : '16k_zh-PY', // 因为内置WebRecorder采样16k的数据，所以参数 engineModelType 需要选择16k的引擎，为 '16k_zh'
    // 以下为非必填参数，可跟据业务自行修改
    // voice_format : 1,
    // hotword_id : '08003a00000000000000000000000000',
    // needvad: 1,
    // filter_dirty: 1,
    // filter_modal: 2,
    // filter_punc: 0,
    // convert_num_mode : 1,
    // word_info: 2
}
let webAudioSpeechRecognizer;
// 发送音频消息
function audioSpeechStar(resultText, callback) {
    // console.log('按钮按下，开始录音');
    webAudioSpeechRecognizer = new WebAudioSpeechRecognizer(params);
    // 开始识别
    webAudioSpeechRecognizer.OnRecognitionStart = (res) => {
        // console.log('开始识别', res);
    };
    // 一句话开始
    webAudioSpeechRecognizer.OnSentenceBegin = (res) => {
        // console.log('一句话开始', res);
    };
    let time_out = 0
    // 识别变化时
    webAudioSpeechRecognizer.OnRecognitionResultChange = (res) => {
        // console.log('识别变化时', res);
        const currentText = `${res.result.voice_text_str}`;
        // console.log('识别变化时-currentText',currentText)
        resultText.value = currentText;
        // if (callback && currentText !== "") {
        //     clearTimeout(time_out);
        //     time_out =  setTimeout(()=>{
        //         webAudioSpeechRecognizer.stop()
        //     },1000)
        // }
    };
    // 一句话结束
    webAudioSpeechRecognizer.OnSentenceEnd = (res) => {
        console.log('一句话结束', res);
        // resultText.value = res.result.voice_text_str;
        // console.log('识别变化时-currentText',resultText)
        if (callback) callback()
    };
    // 识别结束
    webAudioSpeechRecognizer.OnRecognitionComplete = (res) => {
        // console.log('识别结束', res);
    };
    // 识别错误
    webAudioSpeechRecognizer.OnError = (res) => {
        console.log('识别失败', res)
        // resultText.value = `识别失败${res.result}`;
    };
    webAudioSpeechRecognizer.start();

    return webAudioSpeechRecognizer;
}
// 语音停止
function audioSpeechStop(){
    if (webAudioSpeechRecognizer) webAudioSpeechRecognizer.stop()
}
export {
    audioSpeechStar,
    audioSpeechStop
}