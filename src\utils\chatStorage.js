const STORAGE_KEY = 'chatSession'

export function saveChatSession({ userId, conversationId, message, type, avatar }) {
  if (!message || message.trim() === '') return  // ✅ 过滤空白内容

  const raw = sessionStorage.getItem(STORAGE_KEY)
  const history = raw ? JSON.parse(raw) : []

  history.push({
    userId,
    conversationId,
    message,
    type,
    avatar,
    date: new Date().toLocaleTimeString()
  })

  sessionStorage.setItem(STORAGE_KEY, JSON.stringify(history))
}

export function getChatSession() {
  const raw = sessionStorage.getItem(STORAGE_KEY)
  return raw ? JSON.parse(raw) : []
}

export function clearChatSession() {
  sessionStorage.removeItem(STORAGE_KEY)
}

// export function hasWelcomeMessage() {
//   const history = getChatSession()
//   return history.some(
//     item =>
//       item.type === 'system' &&
//       item.message?.includes('哈喽~今天还好吗')
//   )
// }
export function hasWelcomeMessage(text) {
  const history = getChatSession()
  return history.some(item => item.type === 'system' && item.message === text)
}

