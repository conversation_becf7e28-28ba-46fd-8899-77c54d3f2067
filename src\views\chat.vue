<script setup>
import { useRouter } from "vue-router";
import { ref, onMounted, nextTick, reactive, onBeforeUnmount } from "vue";
import { chatMessage } from "@/http/voiceApi.js";
import { getTimeString } from "@/utils/date.js";
import { audioSpeechStar, audioSpeechStop } from "@/utils/audioUtils.js";
import { getStartVoice } from "@/utils/commonUtils.js";
import { Toast } from "antd-mobile";
import { getSystemSettings } from "@/http/settingApi";
// 引入工具函数
import { getChatHistory, getConversationId, saveChatHistory, saveConversationId } from "@/utils/chatHistoryUtils.js";

const router = useRouter();
const message = ref("");
const chatContainer = ref(null);
const maskShow = ref(false);
const btnState = ref(false);
const tipState = ref(false);

// 页面配置
const pageTitle = ref("Terry's Coaching AI");
const defaultAvatar = ref('/images/AIprofile/ai-avatar.png');
const backgroundImage = ref("/images/home/<USER>");
// 聊天消息数组（展示用）
const messageArray = ref([]);
let aiTypingId = 0; // 流式输出ID
let user = "";
let conversation_id = ""; // 从本地获取对话ID（续接对话关键）

// 获取系统设置
const fetchSystemSettings = async () => {
  const settings = await getSystemSettings();
  if (settings) {
    pageTitle.value = settings.title;
    defaultAvatar.value = settings.avatar;
    backgroundImage.value = settings.background;
  }
};

// 页面挂载时加载历史记录
onMounted(async () => {
  await fetchSystemSettings();
  // 1. 加载历史记录（已过滤开场白）
  const history = getChatHistory();
  if (history.length > 0) {
    messageArray.value = history; // 有历史则展示
    await scrollView(); // 滚动到底部
  } else {
    // 2. 无历史记录则显示新对话开场白
    const avatar = defaultAvatar.value;
    const opening = getStartVoice();
    const welcomeMsg = reactive({
      message: "",
      type: "system",
      date: getTimeString(),
      avatar: avatar
    });
    messageArray.value.push(welcomeMsg);
    typeMessage(welcomeMsg, opening.text); // 流式输出开场白
    user = sessionStorage.getItem('user') || '';
    conversation_id = sessionStorage.getItem('conversation_id') || '';
  }
});
onBeforeUnmount(() => {
  saveConversationId(conversation_id); // 用工具函数保存
  sessionStorage.setItem('user', user);
})
// 发送消息（续接对话核心逻辑）
const sendMessage = async () => {
  const text = message.value.trim();
  if (!text) {
    Toast.show("消息不能为空");
    return;
  }
  message.value = "";

  // 添加用户消息
  const userMsg = {
    message: text,
    type: "user",
    user,
    date: getTimeString(),
  };
  messageArray.value.push(userMsg);
  await scrollView();

  // 调用后端接口（携带对话ID确保续接）
  tipState.value = true;
  try {
    const res = await chatMessage({
      text,
      conversation_id, // 从工具函数获取对话ID
      user,
    });
    tipState.value = false;

    if (res.code === 200) {
      // 更新对话ID并保存
      saveConversationId(res.data.conversation_id);
      conversation_id = res.data.conversation_id;
      user = res.data.user;//依据后端返回的user更新用户id

      // 添加AI回复（流式输出）
      const aiMsg = reactive({
        message: "",
        type: "system",
        user,
        date: getTimeString(),
        avatar: defaultAvatar.value
      });
      messageArray.value.push(aiMsg);
      await typeMessage(aiMsg, res.data.answer);

      // 保存最新历史记录
      saveChatHistory(messageArray.value);
      await scrollView();
    }
  } catch (e) {
    tipState.value = false;
    Toast.show("发送失败，请重试");
    console.error("发送消息错误:", e);
  }
};

// AI回复流式输出
async function typeMessage(msgObj, content) {
  aiTypingId++;
  const currentId = aiTypingId;
  msgObj.message = "";
  for (let i = 0; i < content.length; i++) {
    if (currentId!== aiTypingId) return; // 新消息打断旧消息
    msgObj.message += content[i];
    await nextTick();
    await sleep(50);
    await scrollView();
  }
}

// 工具函数：延迟
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 滚动到底部
const scrollView = async () => {
  await nextTick(() => {
    chatContainer.value?.scrollTo({
      top: chatContainer.value.scrollHeight,
      behavior: "smooth"
    });
  });
};

// 语音输入相关
let resultText = ref("");
const handleMouseDown = () => {
  maskShow.value = true;
  audioSpeechStar(resultText);
};
const handleMouseUp = () => {
  maskShow.value = false;
  audioSpeechStop();
  if (resultText.value.trim()) {
    message.value = resultText.value;
    sendMessage();
  }
  resultText.value = "";
};
const handleCancel = () => {
  maskShow.value = false;
  resultText.value = "";
  audioSpeechStop();
};

</script>

<template>
  <div class="chat-container">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="chat-title">
        <h4 style="font-size: 24px; font-weight: bold;">{{ pageTitle }}</h4>
        <img src="@/assets/down.png" alt="下拉菜单" style="margin-top: 15px;"/>
      </div>
      <router-link to="/home">
        <div class="chat-menu">
          <img src="@/assets/chat.png" alt="" />
          <div class="tip">语音互动</div>
        </div>
      </router-link>
    </div>
    <div class="center-gif" :style="{ backgroundImage: `url(${backgroundImage})` }"></div>

    <!-- 空白占位 -->
    <div class="chat-space-placeholder"></div>

    <!-- 聊天消息区域 -->
    <div class="chat-messages" id="chatMessages" ref="chatContainer">
      <div
        class="message"
        :class="item.type === 'system' ? 'received' : 'sent'"
        v-for="(item, index) in messageArray"
        :key="index"
      >
        <!-- AI 回复，显示头像 -->
        <template v-if="item.type === 'system'">
          <div class="ai-avatar">
            <img :src="item.avatar || defaultAvatar" alt="AI Avatar" />
          </div>
          <div class="message-content">
            <div class="message-text">{{ item.message }}</div>
          </div>
        </template>

        <!-- 用户消息 -->
        <template v-else>
          <div class="message-content">
            <div class="message-text">{{ item.message }}</div>
          </div>
        </template>
      </div>

      <!-- 思考中提示 -->
      <div class="tip-box" v-show="tipState">思考中...</div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input-area">
      <div class="message-input">
        <input
          v-if="!btnState"
          type="text"
          v-model="message"
          id="messageInput"
          placeholder="在这输入文字..."
        />
        <div v-if="btnState" @click="handleMouseDown()" class="voice">
          按下说话
        </div>
      </div>
      <div class="input-addons" @click="handleSwitch">
        <img src="@/assets/voice.png" alt="" />
      </div>
      <button class="btn-send" id="sendButton" @click="sendMessage()">
        <img src="@/assets/send.png" alt="" />
      </button>
    </div>

    <!-- 语音遮罩 -->
    <div v-show="maskShow" class="mask">
      <div class="content" style="color: white">{{ resultText }}</div>
      <img class="msg" src="@/assets/message.png" alt="" />
      <div class="btn-box">
        <div class="btn btn-submit" @click="handleMouseUp()">发送文字</div>
        <div class="btn btn-cancel" @click="handleCancel()">取消</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.voice {
  text-align: center;
}
.tip-box {
  color: gray;
  text-indent: 20px;
}
.mask {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  .msg {
    width: 60px;
    height: 30px;
    color: #94eb69;
    position: absolute;
    left: 50%;
    top: 40%;
    transform: translate(-50%, -40%);
  }
  .btn-box {
    display: flex;
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0);
    bottom: 80px;
    .btn {
      background-color: #fff;
      width: 130px;
      height: 50px;
      border-radius: 10px;
      text-align: center;
      line-height: 50px;
      font-size: 22px;
    }
    .btn-submit {
      background-color: #94eb69;
    }
    .btn-cancel {
      margin-left: 30px;
    }
  }
  .content {
    width: 80%;
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0);
    bottom: 170px;
    font-size: 16px;
  }
}
/* 聊天容器 */
.chat-container {
  width: 100%;
  //max-width: 500px;
  //height: calc(100vh - 74px);
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: rgb(247,247,245);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

/* 聊天头部 */
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  //background-color: #007bff;
  background-color: rgb(247,247,245);
  color: white;
  //box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.back-btn,
.chat-menu {
  font-size: 20px;
  cursor: pointer;
}
.chat-menu img {
  width: 60px;
  height: 60px;
}
.chat-menu .tip {
  font-size: 12px;
  color: black;
  font-weight: bold;
  transform: translateX(5px); //  左移 px 对齐图标
}
.back-btn img {
  width: 20px;
  height: 20px;
}
.chat-title {
  display: flex;
}
.chat-title h4 {
  margin: 0;
  font-size: 18px;
  color: black;
}
.chat-title img {
  width: 10px;
  height: 10px;
  margin-top: 5px;
  margin-left: 5px;
}

.chat-space-placeholder {
  height: 25%;
  min-height: 100px; //  一个占位div将消息区域向下挤压
}

/* 聊天消息区域 */
.chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  //background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
  color: black;
  max-height: 55%; // 限制高度为剩下的一半
  overflow-y: auto;
  // position: relative;
}

.message {
  margin-bottom: 15px;
  max-width: 80%;
  display: flex;
  align-items: flex-start; 
}

.message.received {
  align-self: flex-start;
  flex-direction: row;//头像左信息右
}
// .received .message-text {
//   background-color: #f9f9f9 !important;
// }
.message.sent {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-content {
  display: flex;
  flex-direction: column;
}

.message-text {
  padding: 10px 15px;
  border-radius: 18px;
  font-size: 18px;
  line-height: 1.4;
  word-wrap: break-word;
}

.message.received .message-text {
  background-color: rgba(218, 218, 218, 0.8);
  //border-top-left-radius: 4px;
}

.message.sent .message-text {
  //background-color: #007bff;
  color: white;
  background-color: rgba(35, 83, 166, 0.8);
  //border-top-right-radius: 4px;
}

.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  align-self: flex-end;
}

/* 输入区域 */
.chat-input-area {
  display: flex;
  align-items: center;
  //padding: 10px;
  height: 50px;
  width: 92%;
  background-color: white;
  border: 1px solid #eee;
  border-radius: 80px;
  margin: 0 auto;
  margin-bottom: 20px;
  margin-top: 20px;
}

.input-addons {
  display: flex;
  gap: 10px;
}
.input-addons img {
  width: 30px;
  height: 30px;
}
.btn-addon {
  background: none;
  border: none;
  color: #666;
  font-size: 20px;
  cursor: pointer;
  padding: 5px;
}

.message-input {
  flex: 1;
}

.message-input input,
.voice {
  width: 100%;
  padding: 10px 15px;
  border-radius: 20px;
  //border: 1px solid #eee;
  border: none;
  outline: none;
  font-size: 15px;
}

.btn-send {
  //background-color: #007bff;
  color: white;
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  margin-left: 10px;
  margin-right: 10px;
}
.btn-send img {
  width: 35px;
  height: 35px;
}
/* AI头像样式 */
.ai-avatar {
  width: 50px; // 头像宽度
  height: 50px; // 头像高度
  border-radius: 50%; // 圆形头像
  overflow: hidden; // 超出部分隐藏
  margin-right: 10px; // 头像与消息之间的间距
  flex-shrink: 0;    // 防止在小屏幕上被压缩
}

.ai-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover; // 图片填充方式
}
// 背景gif
.center-gif {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 600px; // 可按需调整
  height: 600px;
  max-width: calc(100% - 80px); // 左右各保留20px边距
  transform: translate(-50%, -50%);
  background-image: url('/images/home/<USER>'); // gif背景图的路径
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 0; // 保证在最底层，不遮住内容
  opacity: 0.7; // 可调透明度
  pointer-events: none; // 防止干扰鼠标交互
}
// 设置置于层级一
.chat-header,
.chat-messages,
.chat-input-area {
  position: relative;   //  开启堆叠上下文
  z-index: 1;            //  把它们放在 GIF 之上
}

</style>
